* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow-x: hidden;
    position: relative;
    perspective: 1000px;
}

.container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 450px;
    padding: 20px;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 25px;
    padding: 40px 35px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transform: translateY(0px) scale(1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    border-radius: 25px 25px 0 0;
}

/* SUPER DRAMATIC HOVER EFFECT */
.login-card:hover {
    transform: translateY(-30px) rotateX(15deg) rotateY(10deg) scale(1.1) !important;
    box-shadow:
        0 50px 100px rgba(102, 126, 234, 0.5),
        0 0 100px rgba(255, 154, 158, 0.3),
        inset 0 0 50px rgba(255, 255, 255, 0.2) !important;
    animation: cardGlow 0.5s ease-in-out !important;
}

@keyframes cardGlow {
    0% { filter: brightness(1); }
    50% { filter: brightness(1.2) saturate(1.3); }
    100% { filter: brightness(1.1); }
}

.card-header {
    text-align: center;
    margin-bottom: 35px;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 10px;
}

.logo i {
    font-size: 2.5rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: pulse 2s ease-in-out infinite;
}

.logo h1 {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    color: #666;
    font-size: 0.95rem;
    font-weight: 400;
}

.input-group {
    margin-bottom: 25px;
    position: relative;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper input {
    width: 100%;
    padding: 18px 20px 18px 55px;
    border: 2px solid #e1e5e9;
    border-radius: 15px;
    font-size: 1rem;
    font-weight: 400;
    background: rgba(255, 255, 255, 0.95);
    transition: all 0.2s ease !important;
    outline: none;
    color: #333;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transform: translateY(0) scale(1);
}

/* SUPER DRAMATIC INPUT HOVER */
.input-wrapper input:hover {
    transform: translateY(-5px) scale(1.05) !important;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3) !important;
    border-color: #667eea !important;
}

/* SUPER DRAMATIC INPUT FOCUS */
.input-wrapper input:focus {
    border-color: #667eea !important;
    background: rgba(255, 255, 255, 1) !important;
    transform: translateY(-8px) scale(1.08) !important;
    box-shadow:
        0 20px 50px rgba(102, 126, 234, 0.4) !important,
        0 0 0 5px rgba(102, 126, 234, 0.2) !important;
    animation: inputGlow 0.3s ease !important;
}

@keyframes inputGlow {
    0% { filter: brightness(1); }
    50% { filter: brightness(1.3) saturate(1.5); }
    100% { filter: brightness(1.1); }
}

.input-wrapper input:focus + label {
    transform: translateY(-35px) scale(0.85);
    color: #667eea;
}

.input-wrapper input:not(:placeholder-shown) + label {
    transform: translateY(-35px) scale(0.85);
    color: #667eea;
}

.input-icon {
    position: absolute;
    left: 20px;
    color: #999;
    font-size: 1.1rem;
    z-index: 2;
    transition: all 0.3s ease;
}

.input-wrapper:focus-within .input-icon {
    color: #667eea;
    transform: scale(1.1);
}

label {
    position: absolute;
    left: 55px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-size: 1rem;
    font-weight: 400;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    z-index: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.toggle-password {
    position: absolute;
    right: 20px;
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    z-index: 2;
}

.toggle-password:hover {
    color: #667eea;
    transform: scale(1.1);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    font-size: 0.9rem;
}

.remember-me {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #666;
    font-weight: 400;
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    margin-right: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.remember-me input[type="checkbox"]:checked + .checkmark {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-color: #667eea;
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.forgot-password:hover {
    color: #764ba2;
    text-decoration: underline;
}

.login-btn {
    width: 100%;
    padding: 18px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 15px;
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: all 0.2s ease !important;
    position: relative;
    overflow: hidden;
    margin-bottom: 25px;
    transform: translateY(0) scale(1);
}

/* SUPER DRAMATIC BUTTON HOVER */
.login-btn:hover {
    transform: translateY(-10px) scale(1.15) !important;
    box-shadow: 0 20px 50px rgba(102, 126, 234, 0.6) !important;
    background: linear-gradient(135deg, #7c8ef0 0%, #8a5cb8 100%) !important;
    animation: buttonPulse 0.3s ease !important;
}

@keyframes buttonPulse {
    0% { transform: translateY(-10px) scale(1.15); }
    50% { transform: translateY(-12px) scale(1.2); }
    100% { transform: translateY(-10px) scale(1.15); }
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.login-btn:hover::before {
    left: 100%;
}

.login-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

.login-btn:active {
    transform: translateY(-1px);
}

.divider {
    text-align: center;
    margin: 30px 0;
    position: relative;
    color: #999;
    font-size: 0.9rem;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e1e5e9;
    z-index: 1;
}

.divider span {
    background: rgba(255, 255, 255, 0.95);
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.social-login {
    display: flex;
    gap: 12px;
    margin-bottom: 25px;
}

.social-btn {
    flex: 1;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease !important;
    transform: translateY(0) scale(1);
}

/* SUPER DRAMATIC SOCIAL BUTTON HOVER */
.social-btn:hover {
    transform: translateY(-8px) scale(1.2) !important;
    border-color: transparent !important;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3) !important;
    animation: socialBounce 0.3s ease !important;
}

@keyframes socialBounce {
    0% { transform: translateY(-8px) scale(1.2); }
    50% { transform: translateY(-12px) scale(1.25); }
    100% { transform: translateY(-8px) scale(1.2); }
}

.google-btn:hover {
    background: #db4437;
    color: white;
}

.facebook-btn:hover {
    background: #4267B2;
    color: white;
}

.twitter-btn:hover {
    background: #1DA1F2;
    color: white;
}

.signup-link {
    text-align: center;
    color: #666;
    font-size: 0.95rem;
}

.signup-link a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.signup-link a:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* SUPER SIMPLE FORM TRANSITIONS */
.login-form, .register-form {
    transition: all 0.4s ease !important;
    transform: translateX(0) scale(1);
    opacity: 1;
}

.hidden {
    display: none;
}

.form-header {
    text-align: center;
    margin-bottom: 30px;
}

.form-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.form-header p {
    color: #666;
    font-size: 0.95rem;
}

/* Background Shapes */
.background-shapes {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 154, 158, 0.1), rgba(254, 207, 239, 0.1));
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 300px;
    height: 300px;
    top: -150px;
    left: -150px;
    animation-delay: 0s;
}

.shape-2 {
    width: 200px;
    height: 200px;
    top: 50%;
    right: -100px;
    animation-delay: 2s;
}

.shape-3 {
    width: 150px;
    height: 150px;
    bottom: -75px;
    left: 50%;
    animation-delay: 4s;
}

/* Animations */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(10px) rotate(240deg); }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    animation: slideInNotification 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    max-width: 350px;
    border-left: 4px solid;
}

.notification.success {
    border-left-color: #4CAF50;
    color: #2E7D32;
}

.notification.error {
    border-left-color: #F44336;
    color: #C62828;
}

.notification.warning {
    border-left-color: #FF9800;
    color: #E65100;
}

.notification.info {
    border-left-color: #2196F3;
    color: #1565C0;
}

.close-notification {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    margin-left: auto;
}

.close-notification:hover {
    background: rgba(0, 0, 0, 0.1);
}

/* Floating Particles */
.floating-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: floatParticle linear infinite;
}

/* Input Ripple Effect */
.input-ripple {
    position: absolute;
    top: 50%;
    left: 20px;
    width: 0;
    height: 0;
    background: rgba(102, 126, 234, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: ripple 0.6s ease-out;
    pointer-events: none;
}

/* Additional Animations */
@keyframes slideInNotification {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes floatParticle {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

@keyframes ripple {
    to {
        width: 100px;
        height: 100px;
        opacity: 0;
    }
}

@keyframes slideOutLeft {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(-30px);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(30px);
    }
}

/* Enhanced Input Focus States */
.input-wrapper.focused {
    transform: scale(1.03) translateY(-2px);
}

.input-wrapper.focused input {
    box-shadow:
        0 0 0 3px rgba(102, 126, 234, 0.15),
        0 10px 30px rgba(102, 126, 234, 0.2);
}

/* Enhanced text rendering */
* {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Placeholder styling */
.input-wrapper input::placeholder {
    color: #999;
    opacity: 0.7;
    font-weight: 400;
}

.input-wrapper input:focus::placeholder {
    opacity: 0.5;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 480px) {
    .container {
        padding: 15px;
    }

    .login-card {
        padding: 30px 25px;
    }

    .social-login {
        flex-direction: column;
    }

    .logo h1 {
        font-size: 1.6rem;
    }

    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .shape {
        display: none;
    }
}
